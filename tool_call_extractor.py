#!/usr/bin/env python3
"""
从JSON数据中提取tool_call_sync.go接口的入参
支持从文件读取或直接传入JSON数据，生成多种格式的输出
"""

import json
import argparse
import sys
import re
import uuid
from typing import List, Dict, Any, Optional


class ToolCallParamExtractor:
    """工具调用参数提取器"""
    
    def __init__(self):
        self.extracted_params = []
    
    def extract_from_json_data(self, json_data: Dict[str, Any], 
                              session_id: Optional[int] = None, 
                              session_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """从JSON数据中提取工具调用参数"""
        self.extracted_params = []
        actions = json_data.get('actions_and_results', [])
        
        for i, action_item in enumerate(actions):
            action = action_item.get('action', {})
            function_name = action.get('function_name', '')
            parameters = action.get('parameters', '')
            
            if not function_name:
                continue
            
            # 解析参数字符串
            parsed_params = self._parse_parameters(parameters)
            
            # 构建tool_call_sync.go的入参
            tool_call_param = self._build_tool_call_param(
                function_name, parsed_params, i, session_id, session_code
            )
            
            if tool_call_param:
                self.extracted_params.append(tool_call_param)
        
        return self.extracted_params
    
    def _parse_parameters(self, parameters_str: str) -> Dict[str, str]:
        """解析参数字符串，支持<parameter=key>value</parameter>格式"""
        params = {}
        
        if not parameters_str:
            return params
        
        # 使用正则表达式提取参数
        pattern = r'<parameter=([^>]+)>(.*?)</parameter>'
        matches = re.findall(pattern, parameters_str, re.DOTALL)
        
        for key, value in matches:
            params[key.strip()] = value.strip()
        
        return params
    
    def _build_tool_call_param(self, function_name: str, parsed_params: Dict[str, str], 
                              index: int, session_id: Optional[int] = None, 
                              session_code: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """构建tool_call_sync.go的入参"""
        
        # 生成call_id
        call_id = f"call_{index}_{str(uuid.uuid4())[:8]}"
        
        # 构建arguments JSON
        arguments = json.dumps(parsed_params, ensure_ascii=False)
        
        # 构建tool_call_sync.go的入参结构
        tool_call_param = {
            "session_id": session_id or 1001,
            "session_code": session_code or "",
            "name": function_name,
            "arguments": arguments,
            "call_id": call_id,
            "timeout_seconds": 300
        }
        
        return tool_call_param
    
    def save_to_json(self, output_file: str):
        """保存为JSON格式"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.extracted_params, f, ensure_ascii=False, indent=2)
    
    def save_to_curl(self, output_file: str, api_url: str = "http://localhost:8080/api/v1/mcp/tool/call"):
        """保存为curl命令脚本"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("#!/bin/bash\n")
            f.write("# 自动生成的curl命令\n\n")
            
            for i, param in enumerate(self.extracted_params):
                f.write(f"# 工具调用 {i+1}: {param['name']}\n")
                f.write("curl -X POST \\\n")
                f.write(f"  {api_url} \\\n")
                f.write("  -H 'Content-Type: application/json' \\\n")
                f.write(f"  -d '{json.dumps(param, ensure_ascii=False)}'\n\n")
    
    def save_to_python(self, output_file: str, api_url: str = "http://localhost:8080/api/v1/mcp/tool/call"):
        """保存为Python调用脚本"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("#!/usr/bin/env python3\n")
            f.write("import requests\nimport json\n\n")
            f.write("# 自动生成的Python调用代码\n")
            f.write(f"API_URL = '{api_url}'\n\n")

            for i, param in enumerate(self.extracted_params):
                f.write(f"# 工具调用 {i+1}: {param['name']}\n")
                f.write(f"param_{i+1} = {json.dumps(param, ensure_ascii=False, indent=2)}\n")
                f.write(f"response_{i+1} = requests.post(API_URL, json=param_{i+1})\n")
                f.write(f"print(f'工具调用 {i+1} 响应: {{response_{i+1}.json()}}')\n\n")

    def save_to_command_line(self, output_file: str):
        """保存为命令行格式：一行JSON，一行命令"""
        with open(output_file, 'w', encoding='utf-8') as f:
            for param in self.extracted_params:
                # 写入JSON行
                f.write(json.dumps(param, ensure_ascii=False) + "\n")

                # 解析arguments并生成命令行
                try:
                    args = json.loads(param['arguments'])
                    cmd_line = self._build_command_line(param['name'], args)
                    f.write(cmd_line + "\n")
                except json.JSONDecodeError:
                    # 如果arguments不是有效JSON，使用原始格式
                    f.write(f"{param['name']} {param['arguments']}\n")

    def _build_command_line(self, tool_name: str, args: dict) -> str:
        """根据工具名称和参数构建命令行"""
        cmd_parts = [tool_name]

        for key, value in args.items():
            # 处理特殊字符和换行符
            if isinstance(value, str):
                # 转义特殊字符
                escaped_value = value.replace("'", "\\'").replace('"', '\\"')
                # 如果包含空格或特殊字符，用引号包围
                if ' ' in escaped_value or '\n' in escaped_value or any(c in escaped_value for c in ['&', '|', ';', '<', '>', '(', ')', '$', '`']):
                    cmd_parts.append(f"--{key}='{escaped_value}'")
                else:
                    cmd_parts.append(f"--{key}={escaped_value}")
            else:
                cmd_parts.append(f"--{key}={value}")

        return ' '.join(cmd_parts)
    
    def print_summary(self):
        """打印提取摘要"""
        print(f"提取到 {len(self.extracted_params)} 个工具调用参数:")
        for i, param in enumerate(self.extracted_params):
            print(f"  {i+1}. {param['name']} (call_id: {param['call_id']})")


def main():
    parser = argparse.ArgumentParser(description='从JSON数据中提取tool_call_sync.go接口的入参')
    parser.add_argument('input', nargs='?', help='输入JSON文件路径或JSON字符串')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-f', '--format', choices=['json', 'curl', 'python', 'cmdline'],
                       default='json', help='输出格式 (默认: json)')
    parser.add_argument('--session-id', type=int, default=1001, help='指定session_id (默认: 1001)')
    parser.add_argument('--session-code', default="", help='指定session_code')
    parser.add_argument('--api-url', default="http://localhost:8080/api/v1/mcp/tool/call", 
                       help='API URL (默认: http://localhost:8080/api/v1/mcp/tool/call)')
    parser.add_argument('--stdin', action='store_true', help='从标准输入读取JSON数据')
    
    args = parser.parse_args()
    
    # 读取输入数据
    try:
        if args.stdin:
            json_str = sys.stdin.read()
        elif args.input:
            if args.input.startswith('{'):
                # 直接传入的JSON字符串
                json_str = args.input
            else:
                # 文件路径
                with open(args.input, 'r', encoding='utf-8') as f:
                    json_str = f.read()
        else:
            print("错误: 请提供输入文件路径、JSON字符串或使用--stdin选项", file=sys.stderr)
            sys.exit(1)
        
        json_data = json.loads(json_str)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误: 无法读取或解析JSON数据: {e}", file=sys.stderr)
        sys.exit(1)
    
    # 创建提取器并提取参数
    extractor = ToolCallParamExtractor()
    extracted_params = extractor.extract_from_json_data(
        json_data, args.session_id, args.session_code
    )
    
    # 输出结果
    if args.output:
        if args.format == 'json':
            extractor.save_to_json(args.output)
        elif args.format == 'curl':
            extractor.save_to_curl(args.output, args.api_url)
        elif args.format == 'python':
            extractor.save_to_python(args.output, args.api_url)
        elif args.format == 'cmdline':
            extractor.save_to_command_line(args.output)
        print(f"参数已保存到: {args.output}")
    else:
        # 输出到标准输出
        if args.format == 'json':
            print(json.dumps(extracted_params, ensure_ascii=False, indent=2))
        else:
            print("请指定输出文件路径 (-o) 来生成curl或python格式的文件")
    
    # 打印摘要
    extractor.print_summary()


if __name__ == '__main__':
    main()
