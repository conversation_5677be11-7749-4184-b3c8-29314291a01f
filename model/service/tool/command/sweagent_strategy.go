package command

import (
	"context"
	"fmt"
	"strings"
	"unicode/utf8"

	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

// SweAgentStrategy SweAgent策略实现（遵循r2e项目标准）
type SweAgentStrategy struct{}

// GetStrategyName 获取策略名称
func (s *SweAgentStrategy) GetStrategyName() string {
	return "sweagent"
}

// BuildCommand 构建SweAgent命令
// TODO:  def to_bashcmd(self) -> str: 兼容
// 输入示例: tool_name="file_editor", arguments={"command":"view","path":"/testbed","concise":true}
// 输出示例: ["file_editor", "view", "--path", "/testbed", "--concise", "True"]
func (s *SweAgentStrategy) BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error) {
	command := []string{toolName}

	// 处理command参数（作为子命令）
	if cmd, ok := arguments["command"]; ok {
		command = append(command, fmt.Sprintf("%v", cmd))
	}
	// 为了保证参数顺序的一致性，我们按照特定顺序处理参数
	// 先处理常见的参数，然后处理其他参数
	paramOrder := []string{"path", "concise", "verbose", "count", "input", "output"}

	// 按顺序处理已知参数
	for _, key := range paramOrder {
		if value, ok := arguments[key]; ok && key != "command" {
			command = append(command, fmt.Sprintf("--%s", key))

			// 添加参数值，特殊处理布尔值
			switch v := value.(type) {
			case bool:
				if v {
					command = append(command, "True")
				} else {
					command = append(command, "False")
				}
			default:
				command = append(command, fmt.Sprintf("%v", value))
			}
		}
	}

	// 处理其他未在paramOrder中的参数
	for key, value := range arguments {
		if key == "command" {
			continue // 已经处理过了
		}

		// 检查是否已经在paramOrder中处理过
		found := false
		for _, orderedKey := range paramOrder {
			if key == orderedKey {
				found = true
				break
			}
		}
		if found {
			continue // 已经处理过了
		}

		// 添加参数名
		command = append(command, fmt.Sprintf("--%s", key))

		// 添加参数值，特殊处理布尔值
		switch v := value.(type) {
		case bool:
			if v {
				command = append(command, "True")
			} else {
				command = append(command, "False")
			}
		default:
			command = append(command, fmt.Sprintf("%v", value))
		}
	}

	return command, nil
}

// AssembleResponse 组装SweAgent响应（遵循r2e项目标准）
func (s *SweAgentStrategy) AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	if input.ExecResponse == nil {
		return nil, fmt.Errorf("SweAgent策略需要执行响应对象")
	}

	execResp := input.ExecResponse
	toolName := input.ToolName

	// 根据r2e标准格式化响应内容
	var resultContent string

	// 特殊处理finish和submit工具
	if toolName == "finish" || toolName == "submit" {
		resultContent = "<<< Finished >>>"
	} else {
		// 判断是否为bash类工具
		isBashTool := s.isBashTool(toolName)

		if isBashTool {
			// 对于bash工具，先格式化输出，然后应用中间截断策略
			bashOutput := s.formatBashOutput(execResp)
			truncatedBashOutput := s.maybeTruncateWithLines(bashOutput, 100) // 每边保留100行

			// Bash工具格式: "Exit code: {code}\nExecution output of [{tool_name}]:\n{output}"
			resultContent = fmt.Sprintf("Exit code: %d\nExecution output of [%s]:\n%s",
				execResp.Code, toolName, truncatedBashOutput)
		} else {
			// 其他工具格式: "Execution output of [{tool_name}]:\n{output}"
			toolOutput := s.formatToolOutput(execResp)
			resultContent = fmt.Sprintf("Execution output of [%s]:\n%s",
				toolName, toolOutput)

			// 对于非bash工具，应用简单截断策略
			resultContent = s.simpleTruncate(resultContent)
		}
	}

	// 构建错误消息（用于内部记录）
	var errorMessage string
	if execResp.Code != 0 || execResp.Stderr != "" {
		errorMessage = fmt.Sprintf("errcode: [%d], errmsg: [%s], stdout: [%s], stderr: [%s]",
			execResp.Code, execResp.Message, execResp.Stdout, execResp.Stderr)

		// 限制错误消息长度，MySQL TEXT类型最大65535字节
		const maxErrorMessageLength = 65000 // 留一些缓冲空间
		if len(errorMessage) > maxErrorMessageLength {
			errorMessage = errorMessage[:maxErrorMessageLength] + "...[truncated]"
		}
	}

	// 构建SweAgent格式的响应
	response := &ToolCallResponse{
		CallID:       input.CallID,
		Status:       "success", // SweAgent策略总是返回success，错误信息在Result中体现
		Result:       resultContent,
		ErrorMessage: errorMessage,
		OldEnvMD5:    "", // SweAgent策略不使用环境MD5
		NewEnvMD5:    "",
		OldEnvURL:    "", // SweAgent策略不使用环境URL
		NewEnvURL:    "",
	}

	return response, nil
}

// isBashTool 判断是否为bash类工具
func (s *SweAgentStrategy) isBashTool(toolName string) bool {
	bashTools := []string{"bash", "sh"}
	toolNameLower := strings.ToLower(toolName)

	for _, bashTool := range bashTools {
		if strings.Contains(toolNameLower, bashTool) {
			return true
		}
	}

	return false
}

// formatBashOutput 格式化bash输出
func (s *SweAgentStrategy) formatBashOutput(execResp *rpc_k8s_proxy.ExecCommandResponse) string {
	var output strings.Builder

	// 添加标准输出
	if execResp.Stdout != "" {
		output.WriteString(execResp.Stdout)
	}

	// 如果有错误输出，添加stderr（带🔴前缀）
	if execResp.Stderr != "" {
		if output.Len() > 0 {
			output.WriteString("\n")
		}

		// 按行处理stderr，每行添加🔴前缀
		stderrLines := strings.Split(execResp.Stderr, "\n")
		for i, line := range stderrLines {
			if i > 0 {
				output.WriteString("\n")
			}
			if line != "" {
				output.WriteString("🔴 ")
				output.WriteString(line)
			}
		}
	}

	// 如果没有任何输出，返回空字符串（与Python版本保持一致）
	return output.String()
}

// formatToolOutput 格式化工具输出
func (s *SweAgentStrategy) formatToolOutput(execResp *rpc_k8s_proxy.ExecCommandResponse) string {
	var output strings.Builder

	// 添加标准输出
	if execResp.Stdout != "" {
		output.WriteString(execResp.Stdout)
	}

	// 如果有错误，添加错误信息
	if execResp.Code != 0 || execResp.Stderr != "" {
		if output.Len() > 0 {
			output.WriteString("\n")
		}
		output.WriteString("Error: ")
		if execResp.Message != "" {
			output.WriteString(execResp.Message)
		}
		if execResp.Stderr != "" {
			if execResp.Message != "" {
				output.WriteString(" | ")
			}
			output.WriteString(execResp.Stderr)
		}
	}

	// 如果没有任何输出，返回默认消息
	if output.Len() == 0 {
		return "(no output)"
	}

	return output.String()
}

// simpleTruncate 简单截断策略（用于非bash工具）
func (s *SweAgentStrategy) simpleTruncate(content string) string {
	const maxLength = 65500 // 最大长度限制

	if len(content) <= maxLength {
		return content
	}

	// 确保截断位置是有效的UTF-8字符边界
	truncated := content[:maxLength]
	for len(truncated) > 0 && !utf8.ValidString(truncated) {
		truncated = truncated[:len(truncated)-1]
	}

	return truncated + "...[truncated]"
}

// maybeTruncateWithLines 针对bash输出的中间截断策略
func (s *SweAgentStrategy) maybeTruncateWithLines(content string, numLines int) string {
	lines := strings.Split(content, "\n")

	// 如果行数不超过2倍的保留行数，不需要截断
	if len(lines) <= 2*numLines {
		return content
	}

	// 获取头部行
	topLines := strings.Join(lines[:numLines], "\n")

	// 获取尾部行
	bottomLines := strings.Join(lines[len(lines)-numLines:], "\n")

	// 构建截断后的输出
	divider := strings.Repeat("-", 50)
	truncatedOutput := fmt.Sprintf("%s\n%s\n<Observation truncated in middle for saving context>\n%s\n%s",
		topLines, divider, divider, bottomLines)

	return truncatedOutput
}
