#!/usr/bin/env python3
"""
extract_tool_call_params.py 使用示例
"""

import json
from extract_tool_call_params import ToolCallParamExtractor

# 你提供的示例数据
sample_data = {
    "repo_name": "coveragepy",
    "docker_image": "namanjain12/coveragepy_final:903d2de43ac8c3d9d8ac081bc30d834f3894052d",
    "actions_and_results": [
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>view</parameter>\n  <parameter=path>/testbed</parameter>"
            }
        },
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>create</parameter>\n  <parameter=path>/testbed/reproduce_issue.py</parameter>\n  <parameter=file_text>import coverage\nimport sys\nimport time\n\ndef main():\n    start_time = time.time()\n    \n    # Set a timeout of 20 seconds\n    timeout = 20\n    \n    try:\n        # Create coverage object with debug=sys\n        cov = coverage.Coverage(debug=['sys'])\n        cov.start()\n        \n        # Get system info which should contain data_file\n        info = cov.sys_info()\n        cov.stop()\n        \n        # Check if we're past the timeout\n        if time.time() - start_time > timeout:\n            print(\"Script timed out after\", timeout, \"seconds\")\n            return\n        \n        # Print the data_file value\n        print(\"data_file value:\", info['data_file'])\n        \n        # Check if the value is correct\n        if info['data_file'] == '-none-':\n            print(\"ERROR: data_file is incorrectly set to '-none-'\")\n            sys.exit(1)\n        elif '.coverage' in info['data_file']:\n            print(\"SUCCESS: data_file correctly contains '.coverage'\")\n            sys.exit(0)\n        else:\n            print(\"UNEXPECTED: data_file has unexpected value\")\n            sys.exit(1)\n            \n    except Exception as e:\n        print(f\"An error occurred: {e}\")\n        sys.exit(1)\n\nif __name__ == \"__main__\":\n    main()</parameter>"
            }
        },
        {
            "action": {
                "function_name": "execute_bash",
                "parameters": "<parameter=cmd>python /testbed/reproduce_issue.py</parameter>"
            }
        },
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>view</parameter>\n  <parameter=path>/testbed/coverage/debug.py</parameter>"
            }
        },
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>view</parameter>\n  <parameter=path>/testbed/coverage/control.py</parameter>"
            }
        },
        {
            "action": {
                "function_name": "search",
                "parameters": "<parameter=search_term>sys_info</parameter>\n  <parameter=path>/testbed/coverage</parameter>"
            }
        },
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>view</parameter>\n  <parameter=path>/testbed/coverage/control.py</parameter>\n  <parameter=view_range>[974, 1031]</parameter>"
            }
        },
        {
            "action": {
                "function_name": "file_editor",
                "parameters": "<parameter=command>str_replace</parameter>\n  <parameter=path>/testbed/coverage/control.py</parameter>\n  <parameter=old_str>('data_file', self._data.filename if self._data else \"-none-\"),</parameter>\n  <parameter=new_str>('data_file', self._data.filename if self._data else self.config.data_file),</parameter>"
            }
        }
    ],
    "final_reward": 1
}

def main():
    print("=== extract_tool_call_params.py 使用示例 ===\n")
    
    # 创建提取器
    extractor = ToolCallParamExtractor()
    
    # 提取参数
    print("1. 提取工具调用参数...")
    extracted_params = extractor.extract_from_json_data(
        sample_data, 
        session_id=12345, 
        session_code="test_session_001"
    )
    
    # 显示提取结果
    print("\n2. 提取结果:")
    extractor.print_summary()
    
    print("\n3. 详细参数:")
    for i, param in enumerate(extracted_params):
        print(f"\n参数 {i+1}:")
        print(json.dumps(param, ensure_ascii=False, indent=2))
    
    # 保存到不同格式的文件
    print("\n4. 保存到文件...")
    extractor.save_to_file("tool_call_params.json", "json")
    extractor.save_to_file("tool_call_curl.sh", "curl")
    extractor.save_to_file("tool_call_python.py", "python")
    
    print("已生成以下文件:")
    print("  - tool_call_params.json (JSON格式)")
    print("  - tool_call_curl.sh (curl命令)")
    print("  - tool_call_python.py (Python调用代码)")

if __name__ == '__main__':
    main()
