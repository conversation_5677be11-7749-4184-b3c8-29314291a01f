# Tool Call Parameter Extractor

这是一个Python脚本，用于从JSON数据中提取`tool_call_sync.go`接口的入参。

## 功能特性

- 解析JSON数据中的`actions_and_results`字段
- 提取工具调用的函数名和参数
- 生成符合`tool_call_sync.go`接口要求的入参格式
- 支持多种输出格式：JSON、curl命令、Python代码
- 支持命令行参数配置

## 使用方法

### 基本用法

```bash
# 从文件读取JSON数据，输出JSON格式
python3 tool_call_extractor.py input.json

# 指定输出文件
python3 tool_call_extractor.py input.json -o output.json

# 生成curl命令脚本
python3 tool_call_extractor.py input.json -o commands.sh -f curl

# 生成Python调用脚本
python3 tool_call_extractor.py input.json -o script.py -f python
```

### 命令行参数

- `input`: 输入JSON文件路径或JSON字符串
- `-o, --output`: 输出文件路径
- `-f, --format`: 输出格式 (json/curl/python，默认: json)
- `--session-id`: 指定session_id (默认: 1001)
- `--session-code`: 指定session_code (默认: 空字符串)
- `--api-url`: API URL (默认: http://localhost:8080/api/v1/mcp/tool/call)
- `--stdin`: 从标准输入读取JSON数据

### 示例

#### 1. 基本提取

```bash
python3 tool_call_extractor.py sample.json --session-id 12345 --session-code "test_session"
```

#### 2. 生成curl命令

```bash
python3 tool_call_extractor.py sample.json -o curl_commands.sh -f curl --session-id 12345
```

#### 3. 从标准输入读取

```bash
cat sample.json | python3 tool_call_extractor.py --stdin -o output.json
```

#### 4. 直接传入JSON字符串

```bash
python3 tool_call_extractor.py '{"actions_and_results":[{"action":{"function_name":"test","parameters":"<parameter=key>value</parameter>"}}]}'
```

## 输入格式

脚本期望的JSON输入格式：

```json
{
  "repo_name": "项目名称",
  "docker_image": "镜像名称",
  "actions_and_results": [
    {
      "action": {
        "function_name": "工具名称",
        "parameters": "<parameter=key1>value1</parameter>\n<parameter=key2>value2</parameter>"
      }
    }
  ]
}
```

## 输出格式

### JSON格式

```json
[
  {
    "session_id": 12345,
    "session_code": "test_session",
    "name": "file_editor",
    "arguments": "{\"command\": \"view\", \"path\": \"/testbed\"}",
    "call_id": "call_0_abc12345",
    "timeout_seconds": 300
  }
]
```

### curl命令格式

```bash
#!/bin/bash
# 自动生成的curl命令

# 工具调用 1: file_editor
curl -X POST \
  http://localhost:8080/api/v1/mcp/tool/call \
  -H 'Content-Type: application/json' \
  -d '{"session_id": 12345, "session_code": "test_session", "name": "file_editor", "arguments": "{\"command\": \"view\", \"path\": \"/testbed\"}", "call_id": "call_0_abc12345", "timeout_seconds": 300}'
```

### Python代码格式

```python
#!/usr/bin/env python3
import requests
import json

# 自动生成的Python调用代码
API_URL = 'http://localhost:8080/api/v1/mcp/tool/call'

# 工具调用 1: file_editor
param_1 = {
  "session_id": 12345,
  "session_code": "test_session",
  "name": "file_editor",
  "arguments": "{\"command\": \"view\", \"path\": \"/testbed\"}",
  "call_id": "call_0_abc12345",
  "timeout_seconds": 300
}
response_1 = requests.post(API_URL, json=param_1)
print(f'工具调用 1 响应: {response_1.json()}')
```

## 参数映射

脚本会将输入JSON中的参数映射为`tool_call_sync.go`接口的入参：

| 输入字段 | 输出字段 | 说明 |
|---------|---------|------|
| function_name | name | 工具名称 |
| parameters | arguments | 解析后的参数JSON字符串 |
| - | session_id | 通过命令行参数指定 |
| - | session_code | 通过命令行参数指定 |
| - | call_id | 自动生成的唯一ID |
| - | timeout_seconds | 默认300秒 |

## 注意事项

1. 参数字符串必须使用`<parameter=key>value</parameter>`格式
2. 生成的`call_id`是唯一的，格式为`call_{index}_{uuid}`
3. 默认超时时间为300秒（5分钟）
4. 生成的curl和Python脚本可以直接执行
